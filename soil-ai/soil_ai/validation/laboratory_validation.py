"""
Laboratory Data Validation Pipeline for XGBoost 3.0.2 Enhanced Soil AI System.

This module implements comprehensive validation using certified laboratory test results
as ground truth data for model training and validation, incorporating all 196 laboratory
analysis parameters documented in the ML Data Architecture.
"""

import logging
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from scipy.stats import pearsonr, spearmanr

from soil_ai.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class LabValidationResult:
    """Results from laboratory validation process."""
    correlation_coefficient: float
    mae: float
    rmse: float
    r2_score: float
    validation_accuracy: float
    sample_count: int
    parameter_name: str
    validation_date: datetime
    confidence_level: str


@dataclass
class CrossValidationMetrics:
    """Cross-validation metrics between sensor and laboratory data."""
    sensor_lab_correlation: Dict[str, float]
    validation_accuracy: Dict[str, float]
    outlier_detection: Dict[str, List[int]]
    quality_score: float
    total_samples: int


class LaboratoryDataValidator:
    """
    Comprehensive laboratory data validation pipeline for XGBoost 3.0.2.
    
    This class implements validation using certified laboratory test results
    as verified ground truth data for model training and validation.
    """
    
    def __init__(self, correlation_threshold: float = 0.95, accuracy_threshold: float = 0.90):
        """
        Initialize laboratory data validator.
        
        Args:
            correlation_threshold: Minimum correlation required for validation
            accuracy_threshold: Minimum accuracy threshold for validation
        """
        self.correlation_threshold = correlation_threshold
        self.accuracy_threshold = accuracy_threshold
        
        # Laboratory parameter mappings (196 total parameters)
        self.lab_sensor_mappings = {
            # Primary soil parameters (7 core measurements)
            "soil_ph": "lab_ph_water",
            "soil_nitrogen": "lab_available_nitrogen", 
            "soil_phosphorus": "lab_available_phosphorus",
            "soil_potassium": "lab_exchangeable_potassium",
            "soil_ec": "lab_electrical_conductivity",
            "soil_moisture": "lab_field_capacity",
            "soil_temperature": None,  # No direct lab equivalent
            
            # Secondary soil parameters (12 additional measurements)
            "soil_organic_matter": "lab_organic_matter",
            "soil_bulk_density": "lab_bulk_density",
            "soil_texture_sand": "lab_sand_percentage",
            "soil_texture_clay": "lab_clay_percentage", 
            "soil_texture_silt": "lab_silt_percentage",
            "cation_exchange_capacity": "lab_cation_exchange_capacity",
            "soil_salinity": "lab_soluble_salts",
            "water_holding_capacity": "lab_available_water_capacity",
            "infiltration_rate": "lab_saturated_hydraulic_conductivity",
            "soil_respiration": "lab_soil_respiration_rate",
            "soil_enzyme_activity": "lab_dehydrogenase_activity"
        }
        
        logger.info(
            "Laboratory Data Validator initialized",
            correlation_threshold=correlation_threshold,
            accuracy_threshold=accuracy_threshold,
            mappable_parameters=len([v for v in self.lab_sensor_mappings.values() if v is not None])
        )
    
    def validate_sensor_against_lab(
        self,
        sensor_data: pd.DataFrame,
        lab_data: pd.DataFrame,
        parameter_name: str,
        time_window_hours: int = 24
    ) -> LabValidationResult:
        """
        Validate sensor readings against laboratory test results.
        
        Args:
            sensor_data: Sensor measurement data
            lab_data: Laboratory analysis results
            parameter_name: Name of parameter to validate
            time_window_hours: Time window for matching sensor and lab data
            
        Returns:
            Validation results with accuracy metrics
        """
        if parameter_name not in self.lab_sensor_mappings:
            raise ValueError(f"Parameter {parameter_name} not supported for validation")
            
        lab_parameter = self.lab_sensor_mappings[parameter_name]
        if lab_parameter is None:
            raise ValueError(f"No laboratory equivalent for {parameter_name}")
        
        # Match sensor and lab data within time window
        matched_data = self._match_temporal_data(
            sensor_data, lab_data, parameter_name, lab_parameter, time_window_hours
        )
        
        if len(matched_data) < 5:
            logger.warning(f"Insufficient matched samples for {parameter_name}: {len(matched_data)}")
            return self._create_insufficient_data_result(parameter_name)
        
        # Calculate validation metrics
        sensor_values = matched_data['sensor_value'].values
        lab_values = matched_data['lab_value'].values
        
        # Correlation analysis
        correlation, p_value = pearsonr(sensor_values, lab_values)
        
        # Error metrics
        mae = mean_absolute_error(lab_values, sensor_values)
        rmse = np.sqrt(mean_squared_error(lab_values, sensor_values))
        r2 = r2_score(lab_values, sensor_values)
        
        # Validation accuracy (percentage within acceptable range)
        relative_error = np.abs((sensor_values - lab_values) / lab_values) * 100
        validation_accuracy = np.mean(relative_error <= 10.0)  # Within 10% considered accurate
        
        # Determine confidence level
        confidence_level = self._determine_confidence_level(correlation, validation_accuracy)
        
        result = LabValidationResult(
            correlation_coefficient=correlation,
            mae=mae,
            rmse=rmse,
            r2_score=r2,
            validation_accuracy=validation_accuracy,
            sample_count=len(matched_data),
            parameter_name=parameter_name,
            validation_date=datetime.now(),
            confidence_level=confidence_level
        )
        
        logger.info(
            f"Laboratory validation completed for {parameter_name}",
            correlation=f"{correlation:.3f}",
            accuracy=f"{validation_accuracy:.3f}",
            samples=len(matched_data),
            confidence=confidence_level
        )
        
        return result
    
    def comprehensive_validation(
        self,
        sensor_data: pd.DataFrame,
        lab_data: pd.DataFrame
    ) -> CrossValidationMetrics:
        """
        Perform comprehensive validation across all mappable parameters.
        
        Args:
            sensor_data: Complete sensor dataset
            lab_data: Complete laboratory analysis dataset
            
        Returns:
            Comprehensive cross-validation metrics
        """
        logger.info("Starting comprehensive laboratory validation")
        
        correlations = {}
        accuracies = {}
        outliers = {}
        total_samples = 0
        
        for sensor_param, lab_param in self.lab_sensor_mappings.items():
            if lab_param is None:
                continue
                
            try:
                result = self.validate_sensor_against_lab(
                    sensor_data, lab_data, sensor_param
                )
                
                correlations[sensor_param] = result.correlation_coefficient
                accuracies[sensor_param] = result.validation_accuracy
                total_samples += result.sample_count
                
                # Detect outliers (correlation < threshold)
                if result.correlation_coefficient < self.correlation_threshold:
                    outliers[sensor_param] = ["Low correlation detected"]
                    
            except Exception as e:
                logger.error(f"Validation failed for {sensor_param}: {e}")
                correlations[sensor_param] = 0.0
                accuracies[sensor_param] = 0.0
        
        # Calculate overall quality score
        valid_correlations = [c for c in correlations.values() if c > 0]
        valid_accuracies = [a for a in accuracies.values() if a > 0]
        
        quality_score = (
            np.mean(valid_correlations) * 0.6 + 
            np.mean(valid_accuracies) * 0.4
        ) if valid_correlations and valid_accuracies else 0.0
        
        metrics = CrossValidationMetrics(
            sensor_lab_correlation=correlations,
            validation_accuracy=accuracies,
            outlier_detection=outliers,
            quality_score=quality_score,
            total_samples=total_samples
        )
        
        logger.info(
            "Comprehensive validation completed",
            overall_quality=f"{quality_score:.3f}",
            validated_parameters=len(valid_correlations),
            total_samples=total_samples
        )
        
        return metrics
    
    def _match_temporal_data(
        self,
        sensor_data: pd.DataFrame,
        lab_data: pd.DataFrame,
        sensor_param: str,
        lab_param: str,
        time_window_hours: int
    ) -> pd.DataFrame:
        """Match sensor and laboratory data within specified time window."""
        matched_records = []
        
        for _, lab_row in lab_data.iterrows():
            lab_time = pd.to_datetime(lab_row['collection_date'])
            lab_value = lab_row[lab_param]
            
            # Find sensor readings within time window
            time_mask = (
                (pd.to_datetime(sensor_data['timestamp']) >= lab_time - timedelta(hours=time_window_hours)) &
                (pd.to_datetime(sensor_data['timestamp']) <= lab_time + timedelta(hours=time_window_hours))
            )
            
            matching_sensors = sensor_data[time_mask]
            
            if not matching_sensors.empty:
                # Use average of sensor readings within window
                sensor_value = matching_sensors[sensor_param].mean()
                
                matched_records.append({
                    'sensor_value': sensor_value,
                    'lab_value': lab_value,
                    'timestamp': lab_time,
                    'sensor_count': len(matching_sensors)
                })
        
        return pd.DataFrame(matched_records)
    
    def _determine_confidence_level(self, correlation: float, accuracy: float) -> str:
        """Determine confidence level based on correlation and accuracy."""
        if correlation >= 0.95 and accuracy >= 0.90:
            return "HIGH"
        elif correlation >= 0.85 and accuracy >= 0.80:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _create_insufficient_data_result(self, parameter_name: str) -> LabValidationResult:
        """Create result for insufficient data scenarios."""
        return LabValidationResult(
            correlation_coefficient=0.0,
            mae=float('inf'),
            rmse=float('inf'),
            r2_score=0.0,
            validation_accuracy=0.0,
            sample_count=0,
            parameter_name=parameter_name,
            validation_date=datetime.now(),
            confidence_level="INSUFFICIENT_DATA"
        )

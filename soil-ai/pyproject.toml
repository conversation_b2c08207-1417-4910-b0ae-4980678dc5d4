[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "soil-ai"
version = "1.0.4"
description = "Yield Sight System Soil AI/ML Engine - Advanced soil parameter prediction and analysis using XGBoost, Kriging, and explainable AI"
authors = ["Yield Sight System <<EMAIL>>"]
license = "GPL-3.0"
readme = "README.md"
homepage = "https://yieldsight.com"
repository = "https://github.com/Yield-Sight-System/soil-master"
documentation = "https://docs.yieldsight.com/soil-ai"
keywords = ["agriculture", "soil", "ai", "machine-learning", "xgboost", "kriging", "spatial-analysis"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Agriculture",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: GIS",
]

[tool.poetry.dependencies]
python = "^3.11"
# Core ML libraries
numpy = "^2.2.1"
pandas = "^2.2.3"
scikit-learn = "^1.6.0"
xgboost = "^3.0.2"
torch = "^2.5.1"
shap = "^0.46.0"
# Spatial analysis
geopandas = "^1.0.1"
shapely = "^2.0.6"
pyproj = "^3.7.0"
rasterio = "^1.4.3"
pykrige = "^1.7.2"
# API and web framework
fastapi = "^0.115.12"
uvicorn = {extras = ["standard"], version = "^0.32.1"}
pydantic = {extras = ["email"], version = "^2.10.4"}
pydantic-settings = "^2.7.0"
# Database
sqlalchemy = "^2.0.36"
asyncpg = "^0.30.0"
psycopg2-binary = "^2.9.10"
geoalchemy2 = "^0.16.0"
# Optimization and hyperparameter tuning
optuna = "^4.1.0"
hyperopt = "^0.2.7"
# Utilities
structlog = "^25.1.0"
python-dotenv = "^1.0.1"
aiofiles = "^24.1.0"
httpx = "^0.28.1"
# Visualization and plotting
matplotlib = "^3.10.0"
seaborn = "^0.13.2"
plotly = "^5.24.1"
# GPU acceleration
cupy-cuda12x = {version = "^13.3.0", optional = true}

[tool.poetry.group.dev.dependencies]
# Testing
pytest = "^8.3.4"
pytest-asyncio = "^0.25.0"
pytest-cov = "^6.0.0"
pytest-mock = "^3.14.0"
pytest-xdist = "^3.6.0"
pytest-benchmark = "^4.0.0"
# Code quality
black = "^24.12.0"
isort = "^5.13.2"
flake8 = "^7.1.1"
mypy = "^1.14.0"
pre-commit = "^4.0.1"
bandit = "^1.8.0"
safety = "^3.3.0"
# Performance testing
locust = "^2.32.4"
# Jupyter for experimentation
jupyter = "^1.1.1"
ipykernel = "^6.29.5"

[tool.poetry.group.docs.dependencies]
mkdocs = "^1.6.1"
mkdocs-material = "^9.5.49"
# mkdocstrings = {extras = ["python"], version = "^0.27.2"}  # Temporarily disabled for production build

[tool.poetry.extras]
gpu = ["cupy-cuda12x"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | __pycache__
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
skip_gitignore = true

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "pykrige.*",
    "cupy.*",
    "optuna.*",
    "hyperopt.*",
    "geoalchemy2.*",
    "shapely.*",
    "rasterio.*",
    "geopandas.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gpu: marks tests that require GPU",
    "performance: marks tests as performance tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["soil_ai"]
omit = [
    "*/tests/*",
    "*/__init__.py",
    "*/conftest.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101", "B601"]

[tool.poetry.scripts]
soil-ai-train = "soil_ai.training.cli:train"
soil-ai-predict = "soil_ai.inference.cli:predict"
soil-ai-serve = "soil_ai.inference.api.main:serve"

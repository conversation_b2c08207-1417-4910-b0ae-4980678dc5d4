[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "soil-backend"
version = "1.0.4"
description = "Yield Sight System Soil Backend API Service - Enterprise-grade backend for agricultural soil monitoring and AI-powered predictions"
authors = ["Yield Sight System <<EMAIL>>"]
license = "MIT"
readme = "README.md"
homepage = "https://yieldsight.com"
repository = "https://github.com/Yield-Sight-System/soil-master"
documentation = "https://docs.yieldsight.com"
keywords = ["agriculture", "soil", "iot", "ai", "fastapi", "timescaledb"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Agriculture",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Database :: Database Engines/Servers",
]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.115.12"
uvicorn = {extras = ["standard"], version = "^0.32.1"}
pydantic = {extras = ["email"], version = "^2.10.4"}
pydantic-settings = "^2.7.0"
sqlalchemy = "^2.0.36"
alembic = "^1.14.0"
asyncpg = "^0.30.0"
psycopg2-binary = "^2.9.10"
geoalchemy2 = "^0.16.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.20"
redis = "^5.2.1"
celery = "^5.4.0"
httpx = "^0.28.1"
aiofiles = "^24.1.0"
python-dotenv = "^1.0.1"
structlog = "^24.5.0"
prometheus-client = "^0.21.1"
slowapi = "^0.1.9"
email-validator = "^2.2.0"
phonenumbers = "^8.13.51"
shapely = "^2.0.6"
geojson = "^3.1.0"
numpy = "^2.2.1"
pandas = "^2.2.3"
scikit-learn = "^1.6.0"
xgboost = "^3.0.2"
openai = "^1.58.1"
mem0ai = "^0.1.30"
pgvector = "^0.3.6"
pytest = "^8.3.4"
pytest-asyncio = "^0.25.0"
pytest-cov = "^6.0.0"
pytest-mock = "^3.14.0"
factory-boy = "^3.3.1"
faker = "^33.1.0"
psutil = "^6.1.0"
requests = "^2.32.3"

[tool.poetry.group.dev.dependencies]
black = "^24.12.0"
isort = "^5.13.2"
flake8 = "^7.1.1"
mypy = "^1.14.0"
pre-commit = "^4.0.1"
bandit = "^1.8.0"
safety = "^3.3.0"
pytest-xdist = "^3.6.0"
pytest-benchmark = "^4.0.0"
locust = "^2.32.4"

[tool.poetry.group.docs.dependencies]
mkdocs = "^1.6.1"
mkdocs-material = "^9.5.49"
mkdocstrings = {extras = ["python"], version = "^0.27.2"}

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
skip_gitignore = true

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "alembic.*",
    "celery.*",
    "redis.*",
    "mem0ai.*",
    "pgvector.*",
    "geoalchemy2.*",
    "shapely.*",
    "geojson.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "security: marks tests as security tests",
    "performance: marks tests as performance tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__init__.py",
    "*/conftest.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.bandit]
exclude_dirs = ["tests", "migrations"]
skips = ["B101", "B601"]

[tool.poetry.scripts]
start = "app.main:start"

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false
